#!/usr/bin/env python3
"""
Test script to verify only the PDF deletion related imports work correctly.
"""

import sys
import os

def test_deletion_imports():
    """Test that all imports needed for PDF deletion work correctly."""
    print("Testing PDF deletion imports...")
    
    try:
        # Test content_db import
        from app.utils import content_db
        print("✅ content_db module imported")
        
        # Test database utils
        from app.utils import database as db_utils
        print("✅ database utils imported")
        
        # Test helpers utils
        from app.utils import helpers as utils
        print("✅ helpers utils imported")
        
        # Test orphaned cleanup
        from app.utils.orphaned_pdf_cleanup import OrphanedPDFCleanup
        cleanup = OrphanedPDFCleanup()
        print("✅ OrphanedPDFCleanup imported and instantiated")
        
        # Test specific functions exist
        assert hasattr(content_db, 'get_pdf_by_id'), "get_pdf_by_id not found"
        print("✅ content_db.get_pdf_by_id available")
        
        assert hasattr(db_utils, 'delete_pdf_document_records'), "delete_pdf_document_records not found"
        print("✅ db_utils.delete_pdf_document_records available")
        
        assert hasattr(utils, 'delete_vector_embeddings'), "delete_vector_embeddings not found"
        print("✅ utils.delete_vector_embeddings available")
        
        assert hasattr(utils, 'delete_file'), "delete_file not found"
        print("✅ utils.delete_file available")
        
        # Test OrphanedPDFCleanup methods
        assert hasattr(cleanup, '_file_exists_anywhere'), "_file_exists_anywhere not found"
        assert hasattr(cleanup, '_construct_possible_file_paths'), "_construct_possible_file_paths not found"
        print("✅ OrphanedPDFCleanup methods available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except AssertionError as e:
        print(f"❌ Function availability error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_function_signatures():
    """Test that the functions have the expected signatures."""
    print("\nTesting function signatures...")
    
    try:
        from app.utils import content_db, database as db_utils, helpers as utils
        from app.utils.orphaned_pdf_cleanup import OrphanedPDFCleanup
        
        # Test that we can call the functions with expected parameters
        # (We won't actually call them, just check they exist and can be called)
        
        # Test content_db.get_pdf_by_id
        import inspect
        sig = inspect.signature(content_db.get_pdf_by_id)
        print(f"✅ content_db.get_pdf_by_id signature: {sig}")
        
        # Test db_utils.delete_pdf_document_records
        sig = inspect.signature(db_utils.delete_pdf_document_records)
        print(f"✅ db_utils.delete_pdf_document_records signature: {sig}")
        
        # Test utils.delete_vector_embeddings
        sig = inspect.signature(utils.delete_vector_embeddings)
        print(f"✅ utils.delete_vector_embeddings signature: {sig}")
        
        # Test utils.delete_file
        sig = inspect.signature(utils.delete_file)
        print(f"✅ utils.delete_file signature: {sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing function signatures: {e}")
        return False

def main():
    """Main test function."""
    print("PDF Deletion Import Test (Focused)")
    print("=" * 40)
    
    import_success = test_deletion_imports()
    signature_success = test_function_signatures()
    
    if import_success and signature_success:
        print("\n✅ ALL TESTS PASSED!")
        print("The PDF deletion functionality imports are working correctly.")
        print("\nThe delete routes should work properly when the Flask app is running.")
        print("\nTo test the full functionality:")
        print("1. Start your Flask application")
        print("2. Navigate to a form view: /admin/forms/<form_id>/view")
        print("3. Try deleting a PDF using the delete button")
        print("4. Check the browser console and server logs for any errors")
        return 0
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the import issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
