#!/usr/bin/env python3
"""
Simple test to verify the function signature has been updated correctly.
This test checks the source code directly without importing the modules.
"""

import os
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_upload_regular_pdf_function_signature():
    """Test that upload_regular_pdf_with_ocr_detection has the original_filename parameter"""
    logger.info("Testing upload_regular_pdf_with_ocr_detection function signature...")
    
    try:
        # Read the main app file
        main_file_path = os.path.join('app', '__main__.py')
        
        if not os.path.exists(main_file_path):
            logger.error(f"File not found: {main_file_path}")
            return False
        
        with open(main_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the function definition
        pattern = r'def upload_regular_pdf_with_ocr_detection\((.*?)\):'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            logger.error("Function upload_regular_pdf_with_ocr_detection not found")
            return False
        
        function_params = match.group(1)
        logger.info(f"Found function parameters: {function_params}")
        
        # Check if original_filename parameter is present
        if 'original_filename' not in function_params:
            logger.error("❌ original_filename parameter is missing from function signature")
            return False
        
        # Check if it has a default value
        if 'original_filename=None' not in function_params:
            logger.error("❌ original_filename parameter should have default value of None")
            return False
        
        logger.info("✓ Function signature correctly includes original_filename=None parameter")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        return False

def test_upload_gated_pdf_function_signature():
    """Test that upload_gated_pdf has the original_filename parameter"""
    logger.info("Testing upload_gated_pdf function signature...")
    
    try:
        # Read the main app file
        main_file_path = os.path.join('app', '__main__.py')
        
        if not os.path.exists(main_file_path):
            logger.error(f"File not found: {main_file_path}")
            return False
        
        with open(main_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the function definition
        pattern = r'def upload_gated_pdf\((.*?)\):'
        match = re.search(pattern, content, re.DOTALL)
        
        if not match:
            logger.error("Function upload_gated_pdf not found")
            return False
        
        function_params = match.group(1)
        logger.info(f"Found function parameters: {function_params}")
        
        # Check if original_filename parameter is present
        if 'original_filename' not in function_params:
            logger.error("❌ original_filename parameter is missing from function signature")
            return False
        
        # Check if it has a default value
        if 'original_filename=None' not in function_params:
            logger.error("❌ original_filename parameter should have default value of None")
            return False
        
        logger.info("✓ Function signature correctly includes original_filename=None parameter")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        return False

def test_function_calls_embed_file_db_first_correctly():
    """Test that the function correctly passes original_filename to embed_file_db_first"""
    logger.info("Testing that upload_regular_pdf_with_ocr_detection passes original_filename correctly...")
    
    try:
        # Read the main app file
        main_file_path = os.path.join('app', '__main__.py')
        
        if not os.path.exists(main_file_path):
            logger.error(f"File not found: {main_file_path}")
            return False
        
        with open(main_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the embed_file_db_first call within upload_regular_pdf_with_ocr_detection
        # Look for the function definition first
        func_start = content.find('def upload_regular_pdf_with_ocr_detection(')
        if func_start == -1:
            logger.error("Function upload_regular_pdf_with_ocr_detection not found")
            return False
        
        # Find the next function definition to limit our search
        next_func_start = content.find('\ndef ', func_start + 1)
        if next_func_start == -1:
            func_content = content[func_start:]
        else:
            func_content = content[func_start:next_func_start]
        
        # Look for embed_file_db_first call
        embed_call_pattern = r'embed_file_db_first\((.*?)\)'
        match = re.search(embed_call_pattern, func_content, re.DOTALL)
        
        if not match:
            logger.error("embed_file_db_first call not found in function")
            return False
        
        call_params = match.group(1)
        logger.info(f"Found embed_file_db_first call parameters: {call_params}")
        
        # Check if original_filename is passed
        if 'original_filename=original_filename' not in call_params:
            logger.error("❌ original_filename parameter is not passed to embed_file_db_first")
            return False
        
        logger.info("✓ Function correctly passes original_filename to embed_file_db_first")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        return False

def test_embed_file_db_first_filename_logic():
    """Test the filename logic in embed_file_db_first"""
    logger.info("Testing embed_file_db_first filename logic...")
    
    try:
        # Read the embedding_db file
        embedding_file_path = os.path.join('app', 'utils', 'embedding_db.py')
        
        if not os.path.exists(embedding_file_path):
            logger.error(f"File not found: {embedding_file_path}")
            return False
        
        with open(embedding_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the filename logic
        # Should have: if original_filename: filename = original_filename
        if_original_pattern = r'if original_filename:\s*filename = original_filename'
        if not re.search(if_original_pattern, content):
            logger.error("❌ Missing logic: if original_filename: filename = original_filename")
            return False
        
        # Should have: else: filename = f"{timestamp}_{user_original_filename}"
        else_timestamp_pattern = r'else:\s*filename = f["\'].*?{.*?strftime.*?}.*?{.*?user_original_filename.*?}.*?["\']'
        if not re.search(else_timestamp_pattern, content):
            logger.error("❌ Missing logic for timestamped filename creation")
            return False
        
        logger.info("✓ embed_file_db_first has correct filename logic")
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        return False

def test_batch_upload_duplicate_logic():
    """Test that batch upload correctly uses original_filename for replacement"""
    logger.info("Testing batch upload duplicate replacement logic...")

    try:
        # Read the main app file
        main_file_path = os.path.join('app', '__main__.py')

        if not os.path.exists(main_file_path):
            logger.error(f"File not found: {main_file_path}")
            return False

        with open(main_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check that when duplicate_action == 'replace', we use duplicate_info['original_filename']
        # not duplicate_info['filename']
        correct_pattern = r"original_filename_for_upload = duplicate_info\['original_filename'\]"
        if not re.search(correct_pattern, content):
            logger.error("❌ Batch upload should use duplicate_info['original_filename'] for replacement")
            return False

        # Check that we don't use the wrong pattern
        wrong_pattern = r"original_filename_for_upload = duplicate_info\['filename'\]"
        if re.search(wrong_pattern, content):
            logger.error("❌ Batch upload should NOT use duplicate_info['filename'] for replacement")
            return False

        logger.info("✓ Batch upload correctly uses original_filename for replacement")
        return True

    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    logger.info("Starting function signature and logic tests...")
    logger.info("=" * 60)

    tests = [
        test_upload_regular_pdf_function_signature,
        test_upload_gated_pdf_function_signature,
        test_function_calls_embed_file_db_first_correctly,
        test_embed_file_db_first_filename_logic,
        test_batch_upload_duplicate_logic
    ]

    passed = 0
    failed = 0

    for test in tests:
        logger.info("")
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {str(e)}")
            failed += 1

    logger.info("")
    logger.info("=" * 60)
    logger.info(f"Test Results: {passed} passed, {failed} failed")

    if failed == 0:
        logger.info("🎉 All tests passed! The duplicate replacement fix appears to be implemented correctly.")
        logger.info("")
        logger.info("Summary of changes verified:")
        logger.info("1. ✓ upload_regular_pdf_with_ocr_detection now has original_filename=None parameter")
        logger.info("2. ✓ upload_gated_pdf has original_filename=None parameter")
        logger.info("3. ✓ upload_regular_pdf_with_ocr_detection passes original_filename to embed_file_db_first")
        logger.info("4. ✓ embed_file_db_first has correct filename logic")
        logger.info("5. ✓ Batch upload uses correct original_filename for replacement")
        logger.info("")
        logger.info("Expected behavior:")
        logger.info("- When original_filename is provided: File will keep the original name (no timestamp)")
        logger.info("- When original_filename is None: File will get a timestamped name")
        logger.info("- Duplicate replacement should now preserve original filenames")
        logger.info("- Both single file and batch uploads should work correctly")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")

    return failed == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
