"""
ChromaDB Performance Monitoring Extension

This module provides specialized performance monitoring for ChromaDB vector operations
including similarity search, embedding storage, and collection management.
"""

import time
import logging
import functools
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import json

from app.utils.performance_monitor import get_performance_monitor, DatabaseMetric

logger = logging.getLogger(__name__)

@dataclass
class VectorMetric:
    """Vector database operation performance metric."""
    operation_type: str  # 'similarity_search', 'add_documents', 'delete', 'update'
    collection_name: str
    vector_count: int
    dimension: int
    execution_time: float
    similarity_threshold: Optional[float]
    top_k: Optional[int]
    timestamp: str
    memory_usage_mb: float
    error: Optional[str] = None

class ChromaPerformanceMonitor:
    """Specialized performance monitor for ChromaDB operations."""
    
    def __init__(self):
        self.vector_metrics: List[VectorMetric] = []
        self.collection_stats: Dict[str, Dict] = {}
        
    def add_vector_metric(self, metric: VectorMetric):
        """Add a vector operation metric."""
        self.vector_metrics.append(metric)
        self._update_collection_stats(metric)
        
        # Also add to main performance monitor as database metric
        db_metric = DatabaseMetric(
            operation_type=f"VECTOR_{metric.operation_type.upper()}",
            table_name=metric.collection_name,
            execution_time=metric.execution_time,
            rows_affected=metric.vector_count,
            query_hash=f"chroma_{metric.collection_name}_{metric.operation_type}",
            timestamp=metric.timestamp,
            database_name="chromadb",
            error=metric.error
        )
        
        get_performance_monitor().add_db_metric(db_metric)
    
    def _update_collection_stats(self, metric: VectorMetric):
        """Update aggregated collection statistics."""
        collection = metric.collection_name
        if collection not in self.collection_stats:
            self.collection_stats[collection] = {
                'total_operations': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'search_operations': 0,
                'add_operations': 0,
                'total_vectors': 0,
                'avg_vectors_per_op': 0,
                'error_count': 0
            }
        
        stats = self.collection_stats[collection]
        stats['total_operations'] += 1
        stats['total_time'] += metric.execution_time
        stats['avg_time'] = stats['total_time'] / stats['total_operations']
        
        if metric.operation_type == 'similarity_search':
            stats['search_operations'] += 1
        elif metric.operation_type == 'add_documents':
            stats['add_operations'] += 1
            stats['total_vectors'] += metric.vector_count
            stats['avg_vectors_per_op'] = stats['total_vectors'] / stats['add_operations']
        
        if metric.error:
            stats['error_count'] += 1
    
    def get_collection_stats(self, collection_name: Optional[str] = None) -> Dict[str, Any]:
        """Get collection performance statistics."""
        if collection_name:
            return self.collection_stats.get(collection_name, {})
        return dict(self.collection_stats)
    
    def get_slow_operations(self, threshold_seconds: float = 1.0) -> List[VectorMetric]:
        """Get vector operations that exceeded the time threshold."""
        return [m for m in self.vector_metrics if m.execution_time > threshold_seconds]

# Global ChromaDB performance monitor
_chroma_monitor: Optional[ChromaPerformanceMonitor] = None

def get_chroma_monitor() -> ChromaPerformanceMonitor:
    """Get the global ChromaDB performance monitor instance."""
    global _chroma_monitor
    if _chroma_monitor is None:
        _chroma_monitor = ChromaPerformanceMonitor()
    return _chroma_monitor

def monitor_chroma_operation(
    operation_type: str = None,
    collection_name: str = None,
    track_vectors: bool = True
):
    """
    Decorator for monitoring ChromaDB operations.
    
    Args:
        operation_type: Type of vector operation
        collection_name: Name of the collection
        track_vectors: Whether to track vector count and dimensions
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import psutil
            
            monitor = get_chroma_monitor()
            start_time = time.time()
            
            # Get initial memory
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024
            
            error = None
            vector_count = 0
            dimension = 0
            similarity_threshold = None
            top_k = None

            # Extract operation details before try block to ensure they're always available
            op_type = operation_type or _extract_chroma_operation_type(func.__name__, kwargs)
            collection = collection_name or _extract_collection_name(args, kwargs)

            try:
                result = func(*args, **kwargs)
                
                # Extract vector operation parameters
                if track_vectors:
                    vector_count, dimension = _extract_vector_info(args, kwargs, result)
                    similarity_threshold = kwargs.get('similarity_threshold')
                    top_k = kwargs.get('k') or kwargs.get('n_results')
                
                return result
                
            except Exception as e:
                error = str(e)
                raise
            
            finally:
                end_time = time.time()
                execution_time = end_time - start_time
                
                # Get final memory
                memory_after = process.memory_info().rss / 1024 / 1024
                memory_usage = memory_after - memory_before
                
                # Create vector metric
                metric = VectorMetric(
                    operation_type=op_type or 'unknown',
                    collection_name=collection or 'unknown',
                    vector_count=vector_count,
                    dimension=dimension,
                    execution_time=execution_time,
                    similarity_threshold=similarity_threshold,
                    top_k=top_k,
                    timestamp=datetime.now().isoformat(),
                    memory_usage_mb=memory_usage,
                    error=error
                )
                
                monitor.add_vector_metric(metric)
                
                # Log performance info
                if execution_time > 0.5:  # Log operations taking more than 500ms
                    logger.info(f"ChromaDB {op_type}: {collection} "
                               f"({vector_count} vectors) in {execution_time:.3f}s")
        
        return wrapper
    return decorator

def _extract_chroma_operation_type(func_name: str, kwargs: dict) -> str:
    """Extract ChromaDB operation type from function context."""
    func_lower = func_name.lower()
    
    if 'search' in func_lower or 'query' in func_lower:
        return 'similarity_search'
    elif 'add' in func_lower or 'insert' in func_lower:
        return 'add_documents'
    elif 'update' in func_lower:
        return 'update'
    elif 'delete' in func_lower:
        return 'delete'
    elif 'create' in func_lower:
        return 'create_collection'
    
    return 'unknown'

def _extract_collection_name(args: tuple, kwargs: dict) -> str:
    """Extract collection name from function arguments."""
    # Look for collection name in kwargs
    for key in ['collection_name', 'collection', 'name']:
        if key in kwargs:
            return str(kwargs[key])
    
    # Look for collection object in args
    for arg in args:
        if hasattr(arg, 'name'):
            return str(arg.name)
        elif hasattr(arg, '_name'):
            return str(arg._name)
    
    return 'unknown'

def _extract_vector_info(args: tuple, kwargs: dict, result: Any) -> tuple:
    """Extract vector count and dimension information."""
    vector_count = 0
    dimension = 0
    
    # Try to get from documents being added
    documents = kwargs.get('documents') or kwargs.get('texts')
    if documents and hasattr(documents, '__len__'):
        vector_count = len(documents)
    
    # Try to get from embeddings
    embeddings = kwargs.get('embeddings')
    if embeddings:
        if hasattr(embeddings, '__len__'):
            vector_count = len(embeddings)
            if embeddings and hasattr(embeddings[0], '__len__'):
                dimension = len(embeddings[0])
    
    # Try to get from result
    if result:
        if hasattr(result, '__len__'):
            vector_count = len(result)
        elif hasattr(result, 'get'):
            # ChromaDB query result format
            ids = result.get('ids', [])
            if ids and hasattr(ids[0], '__len__'):
                vector_count = len(ids[0])
    
    # Try to get k parameter for search operations
    k = kwargs.get('k') or kwargs.get('n_results')
    if k and not vector_count:
        vector_count = k
    
    return vector_count, dimension

# Convenience decorators for specific ChromaDB operations
def monitor_similarity_search(func):
    """Decorator for ChromaDB similarity search operations."""
    return monitor_chroma_operation(
        operation_type='similarity_search',
        track_vectors=True
    )(func)

def monitor_add_documents(func):
    """Decorator for ChromaDB document addition operations."""
    return monitor_chroma_operation(
        operation_type='add_documents',
        track_vectors=True
    )(func)

def monitor_collection_operation(func):
    """Decorator for general ChromaDB collection operations."""
    return monitor_chroma_operation(track_vectors=False)(func)

def export_chroma_metrics(filepath: str) -> bool:
    """Export ChromaDB performance metrics to JSON file."""
    try:
        monitor = get_chroma_monitor()
        
        data = {
            'vector_metrics': [asdict(m) for m in monitor.vector_metrics],
            'collection_statistics': monitor.collection_stats,
            'export_timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"ChromaDB metrics exported to {filepath}")
        return True
    except Exception as e:
        logger.error(f"Failed to export ChromaDB metrics: {str(e)}")
        return False
