#!/usr/bin/env python3
"""
Test script to verify the duplicate gated PDF handling fix.

This script tests the specific scenario where:
1. A gated PDF is uploaded initially (creates timestamped filename)
2. The same PDF is re-uploaded with "replace" action
3. Verifies the file is saved correctly without timestamp mismatch

Run this script to verify the fix works correctly.
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def create_test_pdf(filename="test_canopy.pdf"):
    """Create a simple test PDF file."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create a temporary PDF file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # Create PDF content
        c = canvas.Canvas(temp_path, pagesize=letter)
        c.drawString(100, 750, f"Test PDF: {filename}")
        c.drawString(100, 730, f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        c.drawString(100, 710, "This is a test PDF for duplicate handling verification.")
        c.save()
        
        return temp_path
    except ImportError:
        # If reportlab is not available, create a dummy file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf', mode='w')
        temp_file.write(f"Dummy PDF content for {filename}")
        temp_file.close()
        return temp_file.name

class MockFileObject:
    """Mock file object that mimics Flask's FileStorage."""
    
    def __init__(self, file_path, filename):
        self.file_path = file_path
        self.filename = filename
        self._file_obj = None
        self.content_length = os.path.getsize(file_path)
    
    def _get_file_obj(self):
        if self._file_obj is None:
            self._file_obj = open(self.file_path, 'rb')
        return self._file_obj
    
    def read(self, *args, **kwargs):
        return self._get_file_obj().read(*args, **kwargs)
    
    def seek(self, *args, **kwargs):
        return self._get_file_obj().seek(*args, **kwargs)
    
    def tell(self):
        return self._get_file_obj().tell()
    
    def save(self, dest_path):
        """Save the file to the destination path by copying it."""
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        shutil.copy2(self.file_path, dest_path)
        print(f"✅ File saved to: {dest_path}")
    
    def close(self):
        if self._file_obj:
            self._file_obj.close()
            self._file_obj = None

def test_gated_pdf_duplicate_handling():
    """Test the gated PDF duplicate handling fix."""
    print("🧪 Testing Gated PDF Duplicate Handling Fix")
    print("=" * 50)
    
    # Import required functions
    try:
        from app.__main__ import upload_gated_pdf
        from app.utils.helpers import check_duplicate_pdf
        from scripts.setup.create_temp_dirs import create_pdf_directory_structure
    except ImportError as e:
        print(f"❌ Failed to import required modules: {e}")
        return False
    
    # Test parameters
    test_filename = "test_canopy_v44n2.pdf"
    test_category = "TEST_CANOPY"
    test_form_id = 999
    
    # Clean up any existing test files
    test_dir = f"./data/temp/{test_category}"
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"🧹 Cleaned up existing test directory: {test_dir}")
    
    try:
        # Step 1: Create test PDF
        print(f"\n📄 Step 1: Creating test PDF: {test_filename}")
        test_pdf_path = create_test_pdf(test_filename)
        print(f"✅ Test PDF created at: {test_pdf_path}")
        
        # Step 2: First upload (should create timestamped file)
        print(f"\n📤 Step 2: First upload (new file)")
        mock_file_1 = MockFileObject(test_pdf_path, test_filename)
        
        success_1, message_1 = upload_gated_pdf(
            file=mock_file_1,
            category=test_category,
            form_id=test_form_id,
            original_filename=None  # No original filename = new upload
        )
        
        print(f"Upload result: {success_1}, Message: {message_1}")
        
        if not success_1:
            print(f"❌ First upload failed: {message_1}")
            return False
        
        print("✅ First upload successful")
        
        # Step 3: Check what files were created
        print(f"\n📁 Step 3: Checking created files")
        if os.path.exists(test_dir):
            for root, dirs, files in os.walk(test_dir):
                level = root.replace(test_dir, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    print(f"{subindent}{file}")
        
        # Step 4: Check for duplicates
        print(f"\n🔍 Step 4: Checking for duplicates")
        mock_file_2 = MockFileObject(test_pdf_path, test_filename)
        is_duplicate, duplicate_info = check_duplicate_pdf(mock_file_2, test_category)
        
        print(f"Is duplicate: {is_duplicate}")
        if is_duplicate:
            print(f"Duplicate info: {duplicate_info}")
        
        if not is_duplicate:
            print("❌ Expected duplicate detection to find the file")
            return False
        
        print("✅ Duplicate detection working correctly")
        
        # Step 5: Second upload with replacement
        print(f"\n🔄 Step 5: Second upload (replacement)")
        mock_file_3 = MockFileObject(test_pdf_path, test_filename)
        
        # Use the original filename from duplicate_info for replacement
        original_filename_for_replacement = duplicate_info['original_filename']
        print(f"Using original filename for replacement: {original_filename_for_replacement}")
        
        success_2, message_2 = upload_gated_pdf(
            file=mock_file_3,
            category=test_category,
            form_id=test_form_id,
            original_filename=original_filename_for_replacement  # This should preserve original name
        )
        
        print(f"Replacement upload result: {success_2}, Message: {message_2}")
        
        if not success_2:
            print(f"❌ Replacement upload failed: {message_2}")
            return False
        
        print("✅ Replacement upload successful")
        
        # Step 6: Verify the file structure after replacement
        print(f"\n📁 Step 6: Checking file structure after replacement")
        if os.path.exists(test_dir):
            for root, dirs, files in os.walk(test_dir):
                level = root.replace(test_dir, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files:
                    print(f"{subindent}{file}")
        
        # Step 7: Verify the expected file exists
        print(f"\n✅ Step 7: Verifying expected file exists")
        
        # The file should be saved with the original filename (no timestamp)
        expected_dir_structure = create_pdf_directory_structure(test_category, original_filename_for_replacement)
        if expected_dir_structure:
            expected_pdf_path = expected_dir_structure["pdf_path"]
            print(f"Expected PDF path: {expected_pdf_path}")
            
            if os.path.exists(expected_pdf_path):
                print("✅ PDF file found at expected location!")
                return True
            else:
                print(f"❌ PDF file NOT found at expected location: {expected_pdf_path}")
                return False
        else:
            print("❌ Failed to create expected directory structure")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        try:
            if 'test_pdf_path' in locals():
                os.unlink(test_pdf_path)
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
            print(f"\n🧹 Cleaned up test files")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup error: {cleanup_error}")

def main():
    """Main test function."""
    print("🚀 Starting Gated PDF Duplicate Handling Test")
    print("This test verifies the fix for the duplicate file upload issue.")
    print()
    
    success = test_gated_pdf_duplicate_handling()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED! The duplicate handling fix is working correctly.")
    else:
        print("❌ TESTS FAILED! The duplicate handling issue still exists.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
