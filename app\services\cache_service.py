"""
Caching service for ERDB Document Management System
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class CacheService:
    """Centralized caching service"""
    
    def __init__(self):
        self._cache = {}
        self._cache_metadata = {}
        self._default_ttl = 3600  # 1 hour default
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from prefix and arguments"""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key not in self._cache:
            return None
        
        # Check if expired
        if key in self._cache_metadata:
            expiry = self._cache_metadata[key].get('expiry')
            if expiry and datetime.now() > expiry:
                self.delete(key)
                return None
        
        return self._cache[key]
    
    def set(self, key: str, value: Any, ttl: int = None) -> None:
        """Set value in cache with TTL"""
        self._cache[key] = value
        
        ttl = ttl or self._default_ttl
        expiry = datetime.now() + timedelta(seconds=ttl)
        
        self._cache_metadata[key] = {
            'created': datetime.now(),
            'expiry': expiry,
            'ttl': ttl
        }
    
    def delete(self, key: str) -> None:
        """Delete key from cache"""
        self._cache.pop(key, None)
        self._cache_metadata.pop(key, None)
    
    def clear(self) -> None:
        """Clear all cache"""
        self._cache.clear()
        self._cache_metadata.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_keys = len(self._cache)
        expired_keys = sum(
            1 for metadata in self._cache_metadata.values()
            if metadata.get('expiry') and datetime.now() > metadata['expiry']
        )
        
        return {
            'total_keys': total_keys,
            'expired_keys': expired_keys,
            'active_keys': total_keys - expired_keys,
            'cache_size': len(str(self._cache))
        }
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed items"""
        expired_keys = []
        for key, metadata in self._cache_metadata.items():
            if metadata.get('expiry') and datetime.now() > metadata['expiry']:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.delete(key)
        
        return len(expired_keys)

# Global cache instance
cache_service = CacheService()

def cached(prefix: str, ttl: int = None):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {prefix}")
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            logger.debug(f"Cache miss for {prefix}, stored result")
            
            return result
        return wrapper
    return decorator

class ModelCache:
    """Cache for AI model responses"""
    
    @staticmethod
    @cached("model_response", ttl=1800)  # 30 minutes
    def get_model_response(model_name: str, prompt: str, **kwargs) -> str:
        """Get cached model response or generate new one"""
        # This would integrate with your actual model service
        pass
    
    @staticmethod
    @cached("embedding", ttl=86400)  # 24 hours
    def get_embedding(model_name: str, text: str) -> List[float]:
        """Get cached embedding or generate new one"""
        # This would integrate with your actual embedding service
        pass

class DocumentCache:
    """Cache for document processing results"""
    
    @staticmethod
    @cached("pdf_text", ttl=86400)  # 24 hours
    def get_pdf_text(file_path: str) -> str:
        """Get cached PDF text or extract new"""
        # This would integrate with your actual PDF processing
        pass
    
    @staticmethod
    @cached("pdf_images", ttl=86400)  # 24 hours
    def get_pdf_images(file_path: str) -> List[Dict[str, Any]]:
        """Get cached PDF images or extract new"""
        # This would integrate with your actual image extraction
        pass
    
    @staticmethod
    @cached("url_content", ttl=3600)  # 1 hour
    def get_url_content(url: str) -> Dict[str, Any]:
        """Get cached URL content or fetch new"""
        # This would integrate with your actual web scraping
        pass

class QueryCache:
    """Cache for query results"""
    
    @staticmethod
    @cached("query_result", ttl=1800)  # 30 minutes
    def get_query_result(category: str, question: str, model_name: str) -> Dict[str, Any]:
        """Get cached query result or generate new"""
        # This would integrate with your actual query processing
        pass
    
    @staticmethod
    @cached("vector_search", ttl=3600)  # 1 hour
    def get_vector_search(query: str, category: str, k: int = 10) -> List[Dict[str, Any]]:
        """Get cached vector search results or perform new search"""
        # This would integrate with your actual vector search
        pass 