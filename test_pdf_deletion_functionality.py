#!/usr/bin/env python3
"""
Test script for the new PDF deletion functionality in the form view.
This script validates that the new deletion features are properly integrated.
"""

import os
import sys

def validate_backend_routes():
    """Check that the new deletion routes are properly implemented."""
    print("Checking backend routes...")
    
    main_file = 'app/__main__.py'
    if not os.path.exists(main_file):
        print("  [ERROR] Main Flask app file not found")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_routes = [
        'delete_form_pdf',
        'delete_form_pdfs_batch',
        '/admin/forms/<int:form_id>/pdf/<int:pdf_id>/delete',
        '/admin/forms/<int:form_id>/pdfs/delete-batch'
    ]
    
    all_found = True
    for route in required_routes:
        if route in content:
            print(f"  [OK] {route}")
        else:
            print(f"  [MISSING] {route}")
            all_found = False
    
    return all_found

def validate_template_updates():
    """Check that the template has been updated with deletion functionality."""
    print("\nChecking template updates...")
    
    template_file = 'app/templates/admin_form_preview.html'
    if not os.path.exists(template_file):
        print("  [ERROR] Template file not found")
        return False
    
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_elements = [
        'selectAllPdfs',
        'selectNonePdfs', 
        'deleteSelectedPdfs',
        'pdf-checkbox',
        'delete-pdf-btn',
        'file-status-indicator',
        'deleteSinglePdf',
        'deleteBatchPdfs'
    ]
    
    all_found = True
    for element in required_elements:
        if element in content:
            print(f"  [OK] {element}")
        else:
            print(f"  [MISSING] {element}")
            all_found = False
    
    return all_found

def validate_javascript_functionality():
    """Check that JavaScript functionality is properly implemented."""
    print("\nChecking JavaScript functionality...")
    
    template_file = 'app/templates/admin_form_preview.html'
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_js_functions = [
        'updateSelectedCount',
        'checkAllFileStatus',
        'deleteSinglePdf',
        'deleteBatchPdfs',
        'showToast'
    ]
    
    all_found = True
    for func in required_js_functions:
        if func in content:
            print(f"  [OK] {func}")
        else:
            print(f"  [MISSING] {func}")
            all_found = False
    
    return all_found

def validate_safety_features():
    """Check that safety features are implemented."""
    print("\nChecking safety features...")
    
    template_file = 'app/templates/admin_form_preview.html'
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    safety_features = [
        'confirm(',  # Confirmation dialogs
        'X-CSRFToken',  # CSRF protection
        'This action cannot be undone',  # Warning messages
        'error handling'  # Error handling (in comments or code)
    ]
    
    all_found = True
    for feature in safety_features:
        if feature in content:
            print(f"  [OK] {feature}")
        else:
            print(f"  [MISSING] {feature}")
            all_found = False
    
    return all_found

def validate_integration_points():
    """Check integration with existing systems."""
    print("\nChecking integration points...")
    
    main_file = 'app/__main__.py'
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    integration_points = [
        'OrphanedPDFCleanup',  # Uses orphaned cleanup utility
        'delete_pdf_document_records',  # Uses existing deletion functions
        'delete_vector_embeddings',  # Cleans up vector data
        'content_db.get_pdf_by_id',  # Uses existing database functions
        'jsonify'  # Returns JSON responses for AJAX
    ]
    
    all_found = True
    for point in integration_points:
        if point in content:
            print(f"  [OK] {point}")
        else:
            print(f"  [MISSING] {point}")
            all_found = False
    
    return all_found

def check_file_structure():
    """Check that all required files exist."""
    print("\nChecking file structure...")
    
    required_files = [
        'app/__main__.py',
        'app/templates/admin_form_preview.html',
        'app/utils/orphaned_pdf_cleanup.py',
        'app/utils/database.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  [OK] {file_path}")
        else:
            print(f"  [MISSING] {file_path}")
            all_exist = False
    
    return all_exist

def main():
    """Main validation function."""
    print("PDF Deletion Functionality Validation")
    print("=" * 50)
    
    validations = [
        check_file_structure,
        validate_backend_routes,
        validate_template_updates,
        validate_javascript_functionality,
        validate_safety_features,
        validate_integration_points
    ]
    
    results = []
    for validation in validations:
        try:
            result = validation()
            results.append(result)
        except Exception as e:
            print(f"  [ERROR] Validation failed: {str(e)}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total} validations")
    
    if passed == total:
        print("\nALL VALIDATIONS PASSED! PDF deletion functionality is ready for use.")
        print("\nFeatures implemented:")
        print("✅ Individual PDF deletion with confirmation")
        print("✅ Batch PDF deletion with multi-select")
        print("✅ File existence status indicators")
        print("✅ Safety confirmations and error handling")
        print("✅ Integration with existing cleanup utilities")
        print("✅ CSRF protection and secure deletion")
        print("\nUsage:")
        print("1. Navigate to any form view: /admin/forms/<form_id>/view")
        print("2. Use checkboxes to select PDFs for batch deletion")
        print("3. Use individual delete buttons for single PDF removal")
        print("4. Confirm deletions when prompted")
        return 0
    else:
        print(f"\n{total - passed} validations failed. Please review the output above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
