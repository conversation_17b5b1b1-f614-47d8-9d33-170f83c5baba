#!/usr/bin/env python3
"""
Test script to verify Flask app can start without import errors.
"""

import sys
import os

def test_flask_app_imports():
    """Test that the Flask app can be imported without errors."""
    print("Testing Flask app imports...")
    
    try:
        # Set environment variables that might be needed
        os.environ.setdefault('FLASK_ENV', 'development')
        os.environ.setdefault('TEMP_FOLDER', './data/temp')
        
        # Try to import the main Flask app
        from app import __main__
        print("✅ Flask app imported successfully")
        
        # Check if the delete routes are accessible
        app = __main__.app
        
        # Get all routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        # Check for our new delete routes
        delete_routes = [
            '/admin/forms/<int:form_id>/pdf/<int:pdf_id>/delete',
            '/admin/forms/<int:form_id>/pdfs/delete-batch'
        ]
        
        for route in delete_routes:
            if route in routes:
                print(f"✅ Route registered: {route}")
            else:
                print(f"❌ Route missing: {route}")
                return False
        
        print("✅ All delete routes are properly registered")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main test function."""
    print("Flask App Import Test")
    print("=" * 30)
    
    success = test_flask_app_imports()
    
    if success:
        print("\n✅ SUCCESS!")
        print("The Flask app can be imported and the delete routes are registered.")
        print("\nNext steps:")
        print("1. Start your Flask application")
        print("2. Navigate to a form view: /admin/forms/<form_id>/view")
        print("3. Try the PDF deletion functionality")
        return 0
    else:
        print("\n❌ FAILED!")
        print("There are import issues that need to be resolved.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
