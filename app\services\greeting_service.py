import sqlite3
import json
import random
import logging
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GreetingManager:
    """
    Manages greeting templates and provides contextual greeting selection.
    Integrates with the existing database-first architecture.
    Enhanced for Phase 2: Time-based greetings and session awareness.
    """

    def __init__(self, db_path: str = None):
        """Initialize the GreetingManager with database path."""
        self.db_path = db_path or os.getenv("DB_PATH", "./erdb_main.db")
        self.chat_db_path = os.getenv("CHAT_DB_PATH", "chat_history.db")

        # Enhanced fallback greetings with time-based options
        self.fallback_greetings = {
            'welcome': [
                'Hello {name}!',
                'Welcome to the ERDB Knowledge Hub, {name}!',
                'Hi {name}, welcome!'
            ],
            'response': [
                'Hello {name},',
                'Hi {name},',
                'Thank you for your question, {name}.',
                '{name}, here\'s what I found:',
                'I\'d be happy to help with that, {name}.',
                'Great question, {name}.',
                '{name}, based on the information available:',
                'Here\'s what I can tell you, {name}:',
                '{name}, I\'ve analyzed the information and:',
                'Thanks for asking, {name}. Here\'s what I know:'
            ],
            'return_user': [
                'Welcome back, {name}!',
                'Good to see you again, {name}!',
                'Hello again, {name}!'
            ],
            'time_based': {
                'morning': [
                    'Good morning, {name}!',
                    'Good morning, {name}! Ready to explore some knowledge?',
                    'Morning, {name}! How can I help you today?'
                ],
                'afternoon': [
                    'Good afternoon, {name}!',
                    'Good afternoon, {name}! What can I help you with?',
                    'Afternoon, {name}! How\'s your day going?'
                ],
                'evening': [
                    'Good evening, {name}!',
                    'Good evening, {name}! Working late?',
                    'Evening, {name}! How can I assist you tonight?'
                ]
            }
        }

    def get_contextual_greeting(self, client_name: str, context: Dict = None) -> Dict:
        """
        Get appropriate greeting based on context.
        Enhanced for Phase 2: Time-based greetings and session awareness.

        Args:
            client_name: Name of the client
            context: Dictionary containing context information
                    - greeting_type: 'welcome', 'response', 'return_user', 'time_based'
                    - user_role: 'admin', 'editor', 'viewer'
                    - session_type: 'new', 'returning'
                    - time_of_day: 'morning', 'afternoon', 'evening'
                    - device_fingerprint: Device fingerprint for session tracking
                    - session_id: Session ID for analytics
                    - local_time: Client's local time (ISO format)
                    - timezone: Client's timezone

        Returns:
            Dictionary with greeting text and metadata
        """
        try:
            context = context or {}

            # Determine session type and greeting approach
            session_type = self._determine_session_type(context)
            time_of_day = self._determine_time_of_day(context)

            # Choose greeting type based on session and time context
            greeting_type = self._choose_greeting_type(context, session_type, time_of_day)

            # Update context with determined values
            enhanced_context = {
                **context,
                'session_type': session_type,
                'time_of_day': time_of_day,
                'greeting_type': greeting_type
            }

            # Try to get greeting from database
            greeting_data = self._get_database_greeting(greeting_type, enhanced_context)

            if greeting_data:
                greeting_text = self._format_greeting_text(greeting_data['greeting_text'], client_name, enhanced_context)
                return {
                    'greeting': greeting_text,
                    'template_id': greeting_data['id'],
                    'source': 'database',
                    'template_type': greeting_data['template_type'],
                    'session_type': session_type,
                    'time_of_day': time_of_day,
                    'context': enhanced_context
                }
            else:
                # Fallback to hardcoded greetings
                return self._get_fallback_greeting(client_name, greeting_type, enhanced_context)

        except Exception as e:
            logger.error(f"Error getting contextual greeting: {str(e)}")
            return self._get_fallback_greeting(client_name, 'response', context or {})

    def _determine_session_type(self, context: Dict) -> str:
        """Determine if this is a new or returning user session."""
        try:
            device_fingerprint = context.get('device_fingerprint')
            client_name = context.get('client_name')

            if not device_fingerprint:
                return 'new'

            # Check if we have previous sessions for this device/user
            session_count = self._get_session_count(device_fingerprint, client_name)

            if session_count > 0:
                return 'returning'
            else:
                return 'new'

        except Exception as e:
            logger.error(f"Error determining session type: {str(e)}")
            return 'new'

    def _determine_time_of_day(self, context: Dict) -> str:
        """Determine time of day based on client's local time."""
        try:
            # Try to get local time from context
            local_time_str = context.get('local_time')

            if local_time_str:
                # Parse ISO format time
                local_time = datetime.fromisoformat(local_time_str.replace('Z', '+00:00'))
                hour = local_time.hour
            else:
                # Fallback to server time
                hour = datetime.now().hour

            # Determine time of day based on hour
            if 5 <= hour < 12:
                return 'morning'
            elif 12 <= hour < 18:
                return 'afternoon'
            else:
                return 'evening'

        except Exception as e:
            logger.error(f"Error determining time of day: {str(e)}")
            return 'afternoon'  # Safe default

    def _choose_greeting_type(self, context: Dict, session_type: str, time_of_day: str) -> str:
        """Choose the most appropriate greeting type based on context."""
        try:
            # Check if a specific greeting type was requested
            requested_type = context.get('greeting_type')

            if requested_type in ['welcome', 'response', 'return_user', 'time_based']:
                return requested_type

            # Auto-determine based on session type and context
            if session_type == 'new':
                # For new users, prefer time-based welcome greetings
                return 'time_based'
            elif session_type == 'returning':
                # For returning users, mix between return_user and time_based
                if random.choice([True, False]):
                    return 'return_user'
                else:
                    return 'time_based'
            else:
                # Default to response type
                return 'response'

        except Exception as e:
            logger.error(f"Error choosing greeting type: {str(e)}")
            return 'response'

    def _format_greeting_text(self, template_text: str, client_name: str, context: Dict) -> str:
        """Format greeting text with context-aware replacements."""
        try:
            # Basic name replacement
            formatted_text = template_text.format(name=client_name)

            # Add time-based context if available
            time_of_day = context.get('time_of_day')
            if time_of_day and '{time_greeting}' in formatted_text:
                time_greetings = {
                    'morning': 'Good morning',
                    'afternoon': 'Good afternoon',
                    'evening': 'Good evening'
                }
                formatted_text = formatted_text.replace('{time_greeting}', time_greetings.get(time_of_day, 'Hello'))

            return formatted_text

        except Exception as e:
            logger.error(f"Error formatting greeting text: {str(e)}")
            return f"Hello {client_name}!"

    def _get_session_count(self, device_fingerprint: str, client_name: str = None) -> int:
        """Get the number of previous sessions for a device/user."""
        try:
            conn = sqlite3.connect(self.chat_db_path)
            cursor = conn.cursor()

            # Check if the required columns exist
            cursor.execute("PRAGMA table_info(chat_history)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'device_fingerprint' not in column_names:
                return 0

            # Count unique sessions for this device
            if client_name and 'client_name' in column_names:
                cursor.execute('''
                    SELECT COUNT(DISTINCT session_id)
                    FROM chat_history
                    WHERE device_fingerprint = ? AND client_name = ? AND session_id IS NOT NULL
                ''', (device_fingerprint, client_name))
            else:
                cursor.execute('''
                    SELECT COUNT(DISTINCT session_id)
                    FROM chat_history
                    WHERE device_fingerprint = ? AND session_id IS NOT NULL
                ''', (device_fingerprint,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result else 0

        except Exception as e:
            logger.error(f"Error getting session count: {str(e)}")
            return 0

    def _get_database_greeting(self, greeting_type: str, context: Dict) -> Optional[Dict]:
        """Get greeting from database based on type and context."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Build query based on context
            query = '''
                SELECT id, template_type, greeting_text, context_conditions, weight
                FROM greeting_templates
                WHERE template_type = ? AND is_active = 1
                ORDER BY weight DESC
            '''

            cursor.execute(query, (greeting_type,))
            templates = cursor.fetchall()

            if not templates:
                return None

            # Convert to list of dictionaries for easier handling
            template_list = []
            for template in templates:
                template_dict = {
                    'id': template[0],
                    'template_type': template[1],
                    'greeting_text': template[2],
                    'context_conditions': template[3],
                    'weight': template[4]
                }
                template_list.append(template_dict)

            # Select greeting using weighted random selection
            selected_greeting = self._select_weighted_random(template_list)

            conn.close()
            return selected_greeting

        except Exception as e:
            logger.error(f"Database error getting greeting: {str(e)}")
            return None

    def _select_weighted_random(self, templates: List[Dict]) -> Dict:
        """Select a greeting template using weighted random selection."""
        if not templates:
            return None

        # Create weighted list
        weighted_templates = []
        for template in templates:
            weight = template.get('weight', 1)
            weighted_templates.extend([template] * weight)

        # Select random template
        return random.choice(weighted_templates)

    def _get_fallback_greeting(self, client_name: str, greeting_type: str, context: Dict = None) -> Dict:
        """Get fallback greeting when database is unavailable. Enhanced for Phase 2."""
        try:
            context = context or {}
            time_of_day = context.get('time_of_day', 'afternoon')

            # Handle time-based greetings
            if greeting_type == 'time_based':
                time_greetings = self.fallback_greetings['time_based'].get(time_of_day,
                                                                         self.fallback_greetings['time_based']['afternoon'])
                greeting_text = random.choice(time_greetings).format(name=client_name)
            else:
                # Use regular greeting types
                greetings = self.fallback_greetings.get(greeting_type, self.fallback_greetings['response'])
                greeting_text = random.choice(greetings).format(name=client_name)

            return {
                'greeting': greeting_text,
                'template_id': None,
                'source': 'fallback',
                'template_type': greeting_type,
                'session_type': context.get('session_type', 'unknown'),
                'time_of_day': time_of_day,
                'context': context
            }
        except Exception as e:
            logger.error(f"Error getting fallback greeting: {str(e)}")
            return {
                'greeting': f'Hello {client_name}!',
                'template_id': None,
                'source': 'emergency_fallback',
                'template_type': greeting_type,
                'session_type': 'unknown',
                'time_of_day': 'afternoon',
                'context': context or {}
            }

    def log_greeting_usage(self, session_id: str, client_name: str, greeting_data: Dict) -> None:
        """Log greeting usage for analytics. Enhanced for Phase 2."""
        try:
            # Log to greeting analytics table
            if greeting_data.get('source') in ['database', 'fallback']:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # Check if table exists and has required columns
                cursor.execute("PRAGMA table_info(greeting_analytics)")
                columns = cursor.fetchall()

                if columns:  # Table exists
                    cursor.execute('''
                        INSERT INTO greeting_analytics
                        (session_id, client_name, greeting_template_id, greeting_type, timestamp)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        session_id,
                        client_name,
                        greeting_data.get('template_id'),
                        greeting_data.get('template_type'),
                        datetime.now()
                    ))

                    conn.commit()

                conn.close()

            # Also log session metadata for new users
            context = greeting_data.get('context', {})
            if context.get('session_type') == 'new':
                self._log_session_metadata(session_id, client_name, context)

        except Exception as e:
            logger.error(f"Error logging greeting usage: {str(e)}")

    def _log_session_metadata(self, session_id: str, client_name: str, context: Dict) -> None:
        """Log session metadata for new users."""
        try:
            conn = sqlite3.connect(self.chat_db_path)
            cursor = conn.cursor()

            # Create session metadata table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE,
                    client_name TEXT,
                    device_fingerprint TEXT,
                    first_visit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_count INTEGER DEFAULT 1,
                    timezone TEXT,
                    local_time TEXT,
                    greeting_type TEXT,
                    time_of_day TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Insert or update session metadata
            cursor.execute('''
                INSERT OR REPLACE INTO session_metadata
                (session_id, client_name, device_fingerprint, timezone, local_time,
                 greeting_type, time_of_day, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session_id,
                client_name,
                context.get('device_fingerprint'),
                context.get('timezone'),
                context.get('local_time'),
                context.get('greeting_type'),
                context.get('time_of_day'),
                datetime.now()
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error logging session metadata: {str(e)}")

    def get_greeting_templates(self, template_type: str = None) -> List[Dict]:
        """Get all greeting templates, optionally filtered by type."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if template_type:
                cursor.execute('''
                    SELECT id, template_type, greeting_text, context_conditions,
                           is_active, weight, created_at, updated_at
                    FROM greeting_templates
                    WHERE template_type = ?
                    ORDER BY template_type, weight DESC, created_at DESC
                ''', (template_type,))
            else:
                cursor.execute('''
                    SELECT id, template_type, greeting_text, context_conditions,
                           is_active, weight, created_at, updated_at
                    FROM greeting_templates
                    ORDER BY template_type, weight DESC, created_at DESC
                ''')

            templates = cursor.fetchall()
            conn.close()

            # Convert to list of dictionaries
            result = []
            for template in templates:
                result.append({
                    'id': template[0],
                    'template_type': template[1],
                    'greeting_text': template[2],
                    'context_conditions': template[3],
                    'is_active': bool(template[4]),
                    'weight': template[5],
                    'created_at': template[6],
                    'updated_at': template[7]
                })

            return result

        except Exception as e:
            logger.error(f"Error getting greeting templates: {str(e)}")
            return []

    def add_greeting_template(self, template_type: str, greeting_text: str,
                            context_conditions: Dict = None, weight: int = 1) -> bool:
        """Add a new greeting template."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            context_json = json.dumps(context_conditions or {})

            cursor.execute('''
                INSERT INTO greeting_templates
                (template_type, greeting_text, context_conditions, weight)
                VALUES (?, ?, ?, ?)
            ''', (template_type, greeting_text, context_json, weight))

            conn.commit()
            conn.close()

            logger.info(f"Added new greeting template: {template_type}")
            return True

        except Exception as e:
            logger.error(f"Error adding greeting template: {str(e)}")
            return False

    def update_greeting_template(self, template_id: int, **kwargs) -> bool:
        """Update an existing greeting template."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Build update query dynamically
            update_fields = []
            values = []

            for field, value in kwargs.items():
                if field in ['template_type', 'greeting_text', 'is_active', 'weight']:
                    update_fields.append(f"{field} = ?")
                    values.append(value)
                elif field == 'context_conditions':
                    update_fields.append("context_conditions = ?")
                    values.append(json.dumps(value) if isinstance(value, dict) else value)

            if not update_fields:
                return False

            # Add updated_at timestamp
            update_fields.append("updated_at = ?")
            values.append(datetime.now())
            values.append(template_id)

            query = f"UPDATE greeting_templates SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(query, values)

            conn.commit()
            conn.close()

            logger.info(f"Updated greeting template {template_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating greeting template: {str(e)}")
            return False

    def delete_greeting_template(self, template_id: int) -> bool:
        """Delete a greeting template."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM greeting_templates WHERE id = ?", (template_id,))

            conn.commit()
            conn.close()

            logger.info(f"Deleted greeting template {template_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting greeting template: {str(e)}")
            return False

    def get_greeting_analytics(self, start_date: str = None, end_date: str = None) -> Dict:
        """Get greeting analytics for the dashboard. Phase 2 enhancement."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if greeting_analytics table exists
            cursor.execute("PRAGMA table_info(greeting_analytics)")
            columns = cursor.fetchall()

            if not columns:
                return {}

            # Build base query
            base_query = "FROM greeting_analytics WHERE 1=1"
            params = []

            if start_date:
                base_query += " AND timestamp >= ?"
                params.append(f"{start_date} 00:00:00")

            if end_date:
                base_query += " AND timestamp <= ?"
                params.append(f"{end_date} 23:59:59")

            analytics = {}

            # Total greetings
            cursor.execute(f"SELECT COUNT(*) {base_query}", params)
            analytics['total_greetings'] = cursor.fetchone()[0]

            # Greetings by type
            cursor.execute(f"SELECT greeting_type, COUNT(*) {base_query} GROUP BY greeting_type ORDER BY COUNT(*) DESC", params)
            analytics['greetings_by_type'] = [{'type': row[0], 'count': row[1]} for row in cursor.fetchall()]

            # Greetings by time of day (from session metadata)
            try:
                conn_chat = sqlite3.connect(self.chat_db_path)
                cursor_chat = conn_chat.cursor()

                # Check if session_metadata table exists
                cursor_chat.execute("PRAGMA table_info(session_metadata)")
                meta_columns = cursor_chat.fetchall()

                if meta_columns:
                    meta_query = "FROM session_metadata WHERE 1=1"
                    if start_date:
                        meta_query += " AND created_at >= ?"
                    if end_date:
                        meta_query += " AND created_at <= ?"

                    cursor_chat.execute(f"SELECT time_of_day, COUNT(*) {meta_query} GROUP BY time_of_day", params)
                    analytics['greetings_by_time'] = [{'time': row[0], 'count': row[1]} for row in cursor_chat.fetchall()]

                    # Session types
                    cursor_chat.execute(f"SELECT COUNT(*) {meta_query}", params)
                    total_sessions = cursor_chat.fetchone()[0]

                    # Get returning users (those with multiple sessions)
                    cursor_chat.execute(f"SELECT COUNT(DISTINCT device_fingerprint) {meta_query}", params)
                    unique_devices = cursor_chat.fetchone()[0]

                    analytics['session_stats'] = {
                        'total_sessions': total_sessions,
                        'unique_devices': unique_devices,
                        'avg_sessions_per_device': round(total_sessions / unique_devices, 2) if unique_devices > 0 else 0
                    }

                conn_chat.close()

            except Exception as e:
                logger.error(f"Error getting session metadata analytics: {str(e)}")
                analytics['greetings_by_time'] = []
                analytics['session_stats'] = {}

            conn.close()
            return analytics

        except Exception as e:
            logger.error(f"Error getting greeting analytics: {str(e)}")
            return {}

    def get_time_based_engagement_patterns(self) -> Dict:
        """Get engagement patterns by time of day for analytics dashboard."""
        try:
            conn = sqlite3.connect(self.chat_db_path)
            cursor = conn.cursor()

            # Check if session_metadata table exists
            cursor.execute("PRAGMA table_info(session_metadata)")
            columns = cursor.fetchall()

            if not columns:
                return {}

            patterns = {}

            # Engagement by hour (if we have local_time data)
            cursor.execute('''
                SELECT time_of_day, COUNT(*) as count
                FROM session_metadata
                WHERE time_of_day IS NOT NULL
                GROUP BY time_of_day
                ORDER BY
                    CASE time_of_day
                        WHEN 'morning' THEN 1
                        WHEN 'afternoon' THEN 2
                        WHEN 'evening' THEN 3
                        ELSE 4
                    END
            ''')

            time_patterns = cursor.fetchall()
            patterns['by_time_of_day'] = [{'time': row[0], 'count': row[1]} for row in time_patterns]

            # Weekly patterns (if we have enough data)
            cursor.execute('''
                SELECT strftime('%w', created_at) as day_of_week, COUNT(*) as count
                FROM session_metadata
                GROUP BY strftime('%w', created_at)
                ORDER BY day_of_week
            ''')

            weekly_patterns = cursor.fetchall()
            day_names = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
            patterns['by_day_of_week'] = [
                {'day': day_names[int(row[0])], 'count': row[1]}
                for row in weekly_patterns
            ]

            conn.close()
            return patterns

        except Exception as e:
            logger.error(f"Error getting time-based engagement patterns: {str(e)}")
            return {}

    def get_greeting(self) -> str:
        """Get the current default greeting. Legacy method for backward compatibility."""
        try:
            # Get a random response greeting as the default
            templates = self.get_greeting_templates('response')
            if templates:
                # Return the first active template
                for template in templates:
                    if template.get('is_active', True):
                        return template['greeting_text']
            
            # Fallback to hardcoded greeting
            return random.choice(self.fallback_greetings['response'])
            
        except Exception as e:
            logger.error(f"Error getting greeting: {str(e)}")
            return "Hello there!"

    def set_greeting(self, greeting_text: str) -> bool:
        """Set a new default greeting. Legacy method for backward compatibility."""
        try:
            # Validate input
            if not greeting_text or not greeting_text.strip():
                logger.error("Cannot set empty greeting text")
                return False
            
            # Add as a new response template
            success = self.add_greeting_template(
                template_type='response',
                greeting_text=greeting_text.strip(),
                context_conditions={},
                weight=10  # Higher weight to make it more likely to be selected
            )
            
            if success:
                logger.info(f"Set new default greeting: {greeting_text}")
                return True
            else:
                logger.error("Failed to set new greeting")
                return False
                
        except Exception as e:
            logger.error(f"Error setting greeting: {str(e)}")
            return False
