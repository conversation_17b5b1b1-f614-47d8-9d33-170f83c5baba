# Duplicate Gated PDF Upload Fix

## Issue Summary

**Problem**: When re-uploading gated PDF files with "replace" action, the system was creating timestamped directory structures but the actual PDF files were missing from their expected locations.

**Error Pattern**:
```
ERROR:__main__:Gated PDF file not found at expected location: data\temp\CANOPY\20250802052507_canopy_v44n2\20250802052507_canopy_v44n2.pdf
ERROR:__main__:Directory structure: {...}
ERROR:__main__:Files found in PDF directory: ['pdf_images', 'pdf_tables', 'pdf_text']
```

**Key Observation**: Directory structures were created correctly, but the main PDF file was missing.

## Root Cause Analysis

The issue was in the `upload_gated_pdf` function in `app/__main__.py`. Here's what was happening:

### The Problem Flow

1. **Duplicate Detection** (Batch Upload):
   - User uploads `canopy_v44n2.pdf` which already exists as `20250801123456_canopy_v44n2.pdf`
   - User selects "replace" action
   - System sets `original_filename_for_upload = duplicate_info['original_filename']` = `"canopy_v44n2.pdf"`
   - Old timestamped file gets deleted

2. **Gated PDF Upload Function** (Lines 4566-4582):
   ```python
   # PROBLEMATIC CODE (before fix):
   filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}"
   # This generated: "20250802052507_canopy_v44n2.pdf"
   
   success, message = embed_file_db_first(
       file, 
       category, 
       # ...
       original_filename=original_filename,  # This was "canopy_v44n2.pdf"
       # ...
   )
   ```

3. **File Saving in embed_file_db_first** (Lines 64-70):
   ```python
   if original_filename:
       filename = original_filename  # Uses "canopy_v44n2.pdf"
   else:
       filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{user_original_filename}"
   ```
   - File gets saved to directory structure based on `"canopy_v44n2"` (no timestamp)

4. **Directory Structure Creation** (Lines 4592-4615):
   ```python
   dir_structure = create_pdf_directory_structure(category, filename)
   # But 'filename' was still "20250802052507_canopy_v44n2.pdf"
   dest = dir_structure["pdf_path"]
   # Expected path: data/temp/CANOPY/20250802052507_canopy_v44n2/20250802052507_canopy_v44n2.pdf
   ```

5. **File Location Mismatch**:
   - File saved to: `data/temp/CANOPY/canopy_v44n2/canopy_v44n2.pdf`
   - File expected at: `data/temp/CANOPY/20250802052507_canopy_v44n2/20250802052507_canopy_v44n2.pdf`
   - Result: File not found error

### The Core Issue

Both `upload_gated_pdf` and `upload_regular_pdf_with_ocr_detection` functions were generating their own timestamped filenames and using those for directory structure creation, while simultaneously passing the original filename (without timestamp) to `embed_file_db_first` for file saving. This created a mismatch between where the file was saved and where it was expected to be found.

## Solution Implemented

### Fix in `upload_gated_pdf` Function

**Before (Problematic)**:
```python
# Always generated timestamped filename
filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}"

success, message = embed_file_db_first(
    # ...
    original_filename=original_filename,  # Inconsistent with 'filename'
    # ...
)

# Used the timestamped 'filename' for directory structure
dir_structure = create_pdf_directory_structure(category, filename)
```

**After (Fixed)**:
```python
# Determine filename based on whether this is a replacement or new upload
if original_filename:
    # For replacement: use the original filename (no timestamp)
    filename = original_filename
    logger.info(f"Using provided filename for replacement: {filename}")
else:
    # For new upload: generate timestamped filename
    filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}"
    logger.info(f"Generated timestamped filename for new upload: {filename}")

success, message = embed_file_db_first(
    # ...
    original_filename=filename,  # Now consistent
    # ...
)

# Use the same 'filename' for directory structure
dir_structure = create_pdf_directory_structure(category, filename)
```

### Key Changes

1. **Conditional Filename Generation**: Only generate timestamped filename for new uploads, not replacements
2. **Consistent Filename Usage**: Use the same filename for both file saving and directory structure creation
3. **Proper Parameter Passing**: Pass the determined filename to `embed_file_db_first`

## Expected Behavior After Fix

### For New Uploads (original_filename=None):
1. Generate timestamped filename: `20250802052507_canopy_v44n2.pdf`
2. Save file to: `data/temp/CANOPY/20250802052507_canopy_v44n2/20250802052507_canopy_v44n2.pdf`
3. Create directory structure based on timestamped filename

### For Replacement Uploads (original_filename="canopy_v44n2.pdf"):
1. Use provided filename: `canopy_v44n2.pdf`
2. Save file to: `data/temp/CANOPY/canopy_v44n2/canopy_v44n2.pdf`
3. Create directory structure based on original filename (no timestamp)

## Files Modified

- **`app/__main__.py`** (Lines 4566-4590): Fixed filename determination logic in `upload_gated_pdf` function
- **`app/__main__.py`** (Lines 4301-4322): Fixed filename determination logic in `upload_regular_pdf_with_ocr_detection` function

## Testing

A comprehensive test script has been created: `test_duplicate_gated_pdf_fix.py`

**Test Scenarios**:
1. ✅ Initial gated PDF upload (creates timestamped file)
2. ✅ Duplicate detection works correctly
3. ✅ Replacement upload uses original filename
4. ✅ File is saved to correct location
5. ✅ Directory structure matches file location

**Run the test**:
```bash
python test_duplicate_gated_pdf_fix.py
```

## Verification Steps

To verify the fix works:

1. **Upload a gated PDF** (should create timestamped directory)
2. **Re-upload the same PDF** with "replace" action
3. **Check that**:
   - No "file not found" errors occur
   - File is saved to directory structure based on original filename
   - Directory structure and file location are consistent

## Prevention

To prevent similar issues in the future:

1. **Consistent Filename Handling**: Always use the same filename for both file saving and directory structure creation
2. **Clear Parameter Naming**: Use descriptive parameter names to avoid confusion
3. **Comprehensive Testing**: Test both new uploads and replacement scenarios
4. **Logging**: Add detailed logging to track filename transformations

## Summary

This comprehensive fix addresses the duplicate file upload issue that was affecting both gated and regular PDF uploads. The root cause was inconsistent filename handling between file saving and directory structure creation during replacement operations.

**Key Achievements:**
- ✅ Fixed both `upload_gated_pdf` and `upload_regular_pdf_with_ocr_detection` functions
- ✅ Ensured consistent filename handling across all upload scenarios
- ✅ Maintained backward compatibility for new uploads
- ✅ Preserved original filenames during replacement operations
- ✅ Eliminated file location mismatches
- ✅ Added comprehensive test coverage

## Related Issues

This fix resolves the regression introduced during recent duplicate handling improvements and ensures that:
- ✅ Duplicate detection works correctly
- ✅ File replacement preserves original filenames
- ✅ Directory structures are created consistently
- ✅ Files are saved to expected locations
- ✅ No file location mismatches occur
- ✅ Both gated and regular PDF uploads work correctly
