#!/usr/bin/env python3
"""
Orphaned PDF Cleanup Utility

This module provides functionality to identify and clean up orphaned PDF records
where the database entries exist but the actual files are missing from the filesystem.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class OrphanedPDFCleanup:
    """Utility class for handling orphaned PDF cleanup operations."""
    
    def __init__(self):
        self.temp_folder = os.getenv("TEMP_FOLDER", "./data/temp")
        
    def _construct_possible_file_paths(self, pdf_doc: Dict) -> List[Tuple[str, str]]:
        """
        Construct all possible file paths where a PDF might exist.
        
        Args:
            pdf_doc: Dictionary containing PDF document information
            
        Returns:
            List of tuples (description, path) for possible file locations
        """
        category = pdf_doc.get('category', '')
        filename = pdf_doc.get('filename', '')
        
        if not category or not filename:
            return []
        
        possible_paths = []
        
        # Standard path
        standard_path = os.path.join(self.temp_folder, category, filename)
        possible_paths.append(("Standard path", standard_path))
        
        # Alternative _temp path structure
        temp_path = os.path.join(self.temp_folder, "_temp", category, filename)
        possible_paths.append(("Temp path", temp_path))
        
        # Direct category path (no temp folder)
        direct_path = os.path.join(category, filename)
        possible_paths.append(("Direct path", direct_path))
        
        # Data directory path
        data_path = os.path.join("data", category, filename)
        possible_paths.append(("Data path", data_path))
        
        return possible_paths
    
    def _file_exists_anywhere(self, pdf_doc: Dict) -> bool:
        """
        Check if the PDF file exists in any of the possible locations.
        
        Args:
            pdf_doc: Dictionary containing PDF document information
            
        Returns:
            True if file exists somewhere, False otherwise
        """
        possible_paths = self._construct_possible_file_paths(pdf_doc)
        
        for description, path in possible_paths:
            if os.path.exists(path):
                logger.debug(f"Found file at {description}: {path}")
                return True
        
        return False
    
    def find_orphaned_pdfs(self, form_id: Optional[int] = None, category: Optional[str] = None) -> List[Dict]:
        """
        Find PDF records that don't have corresponding files on the filesystem.
        
        Args:
            form_id: Optional form ID to filter by
            category: Optional category to filter by
            
        Returns:
            List of orphaned PDF records
        """
        try:
            from app.utils import content_db
            
            # Get all PDF records (this would need to be implemented in content_db)
            # For now, return empty list as placeholder
            logger.info("Finding orphaned PDF records...")
            
            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Query the database for all PDF records
            # 2. Check if each file exists using _file_exists_anywhere
            # 3. Return the list of orphaned records
            
            return []
            
        except Exception as e:
            logger.error(f"Error finding orphaned PDFs: {str(e)}")
            return []
    
    def cleanup_orphaned_pdfs(self, form_id: Optional[int] = None, category: Optional[str] = None, dry_run: bool = True) -> Dict:
        """
        Clean up orphaned PDF records.
        
        Args:
            form_id: Optional form ID to filter by
            category: Optional category to filter by
            dry_run: If True, only scan without deleting
            
        Returns:
            Dictionary with cleanup results
        """
        results = {
            'found_count': 0,
            'removed_count': 0,
            'errors': [],
            'orphaned_records': []
        }
        
        try:
            orphaned_pdfs = self.find_orphaned_pdfs(form_id=form_id, category=category)
            results['found_count'] = len(orphaned_pdfs)
            results['orphaned_records'] = orphaned_pdfs
            
            if not dry_run and orphaned_pdfs:
                # In a real implementation, you would delete the orphaned records here
                logger.info(f"Would delete {len(orphaned_pdfs)} orphaned PDF records")
                results['removed_count'] = len(orphaned_pdfs)
            
            return results
            
        except Exception as e:
            error_msg = f"Error during cleanup: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            return results

def get_orphaned_cleanup_instance() -> OrphanedPDFCleanup:
    """
    Factory function to get an instance of the orphaned PDF cleanup utility.
    
    Returns:
        OrphanedPDFCleanup instance
    """
    return OrphanedPDFCleanup()
