import os
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
import logging
import requests
from chromadb.config import Settings
import json

# Import ChromaDB performance monitoring
from app.utils.chroma_performance import (
    monitor_chroma_operation,
    monitor_similarity_search,
    monitor_add_documents,
    get_chroma_monitor
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Update to use unified database path
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]

def check_embedding_model_availability(model_name):
    """Check if the specified embedding model is available in Ollama."""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        if response.status_code != 200:
            logger.warning(f"Ollama service returned status code: {response.status_code}")
            return False

        # Check if the embedding model is available
        models = response.json().get("models", [])
        model_names = [model.get("name") for model in models]

        if model_name in model_names:
            logger.info(f"Embedding model '{model_name}' is available")
            return True
        else:
            logger.warning(f"Embedding model '{model_name}' not found in available models: {', '.join(model_names)}")
            return False
    except Exception as e:
        logger.warning(f"Failed to check embedding model availability: {str(e)}")
        return False

def _get_embedding_prompts():
    try:
        with open(os.path.join(os.path.dirname(__file__), '../../config/default_models.json'), 'r') as f:
            config = json.load(f)
        params = config.get('embedding_parameters', {})
        return params.get('embedding_prompt', ''), params.get('query_prompt', '')
    except Exception as e:
        logger.warning(f"Could not load embedding prompts from config: {e}")
        return '', ''

# Remove PromptedOllamaEmbeddings and always use OllamaEmbeddings batching for all models

# Cache for Chroma instances
_chroma_cache = {}

def get_vector_db(category: str) -> Chroma:
    """
    Get or create a Chroma vector database for the specified category.
    Now uses unified database with metadata filtering.

    Args:
        category (str): The category name for the vector database.

    Returns:
        Chroma: The Chroma vector database instance.
    """
    try:
        # Return cached instance if available
        if category in _chroma_cache:
            logger.debug(f"Reusing cached Chroma DB for category: {category}")
            return _chroma_cache[category]

        # Try to initialize with the configured embedding model
        model_to_use = TEXT_EMBEDDING_MODEL

        # Check if the model is available
        if not check_embedding_model_availability(model_to_use):
            # Try fallback models if the primary one isn't available
            for fallback_model in FALLBACK_EMBEDDING_MODELS:
                if fallback_model != model_to_use and check_embedding_model_availability(fallback_model):
                    logger.info(f"Using fallback embedding model: {fallback_model}")
                    model_to_use = fallback_model
                    break

        logger.info(f"Initializing embedding function with model: {model_to_use}")
        try:
            embedding_prompt, query_prompt = _get_embedding_prompts()
            embed_fn = OllamaEmbeddings(model=model_to_use)
            logger.info(f"Successfully initialized embedding function with model: {model_to_use}")
        except Exception as embed_error:
            logger.error(f"Failed to initialize embedding function with model {model_to_use}: {str(embed_error)}")
            # Last resort fallback to nomic-embed-text
            logger.info("Trying last resort fallback to nomic-embed-text:latest")
            try:
                embed_fn = OllamaEmbeddings(model="nomic-embed-text:latest")
                logger.info("Successfully initialized fallback embedding function with nomic-embed-text:latest")
            except Exception as fallback_error:
                logger.error(f"Failed to initialize fallback embedding model: {str(fallback_error)}")
                raise ValueError(f"Could not initialize any embedding model. Original error: {str(embed_error)}, Fallback error: {str(fallback_error)}")

        # Create the unified vector database
        persist_dir = CHROMA_PATH
        os.makedirs(persist_dir, exist_ok=True)
        logger.info(f"Initializing unified Chroma vector DB at {persist_dir}")

        try:
            # Disable telemetry
            telemetry_settings = Settings(anonymized_telemetry=False)
            
            # Use unified collection name
            db = Chroma(
                collection_name="unified_collection",
                persist_directory=persist_dir,
                embedding_function=embed_fn,
                client_settings=telemetry_settings
            )

            # Cache the instance
            _chroma_cache[category] = db
            return db

        except ValueError as ve:
            # Handle dimension mismatch error specifically
            error_msg = str(ve).lower()
            if "embedding dimension" in error_msg and "does not match collection dimensionality" in error_msg:
                # Extract dimensions from error message
                import re
                match = re.search(r'embedding dimension (\d+) does not match collection dimensionality (\d+)', error_msg)
                if match:
                    current_dim = match.group(1)
                    collection_dim = match.group(2)

                    detailed_error = (
                        f"Dimension mismatch error: Your current embedding model ({model_to_use}) "
                        f"produces {current_dim}-dimensional vectors, but the existing collection "
                        f"was created with {collection_dim}-dimensional vectors.\n\n"
                        f"To fix this issue, you have two options:\n"
                        f"1. Run 'python switch_embedding_model.py --auto' to automatically switch to a compatible model\n"
                        f"2. Run 'python reembed_documents.py' to re-embed all documents with your current model\n\n"
                        f"See the documentation for more details on these options."
                    )
                    logger.error(detailed_error)
                    raise ValueError(detailed_error) from ve

            # Re-raise the original error if it's not a dimension mismatch
            raise

    except Exception as e:
        logger.error(f"Failed to initialize vector DB for category {category}: {str(e)}")

        # Provide more detailed error information for common issues
        error_msg = str(e).lower()
        if "str object has no attribute get" in error_msg:
            logger.error("This appears to be an issue with model initialization. Check that Ollama is running and the embedding model is available.")
        elif "connection refused" in error_msg:
            logger.error("Could not connect to Ollama. Make sure the Ollama service is running.")
        elif "no such file or directory" in error_msg:
            logger.error(f"The directory for category {category} does not exist or is not accessible.")

        raise

@monitor_similarity_search
def similarity_search_with_category_filter(query: str, category: str, k: int = 10, **kwargs):
    """
    Perform similarity search with category filtering.
    
    Args:
        query: Search query
        category: Category to filter by
        k: Number of results to return
        **kwargs: Additional search parameters
        
    Returns:
        List of similar documents
    """
    try:
        db = get_vector_db(category)
        
        # Use metadata filtering for category separation
        filter_dict = {"category": category}
        
        results = db.similarity_search(
            query,
            k=k,
            filter=filter_dict,
            **kwargs
        )
        
        logger.info(f"Found {len(results)} documents for query in category {category}")
        return results
        
    except Exception as e:
        logger.error(f"Failed to perform similarity search: {str(e)}")
        raise

@monitor_add_documents
def add_documents_with_category(documents, category: str, **kwargs):
    """
    Add documents to the unified database with category metadata.
    
    Args:
        documents: List of documents to add
        category: Category for the documents
        **kwargs: Additional metadata
    """
    try:
        db = get_vector_db(category)
        
        # Prepare documents with category metadata
        enhanced_documents = []
        for doc in documents:
            # Create a copy of the document with enhanced metadata
            enhanced_metadata = doc.metadata.copy() if hasattr(doc, 'metadata') else {}
            enhanced_metadata['category'] = category
            enhanced_metadata.update(kwargs)
            
            # Create new document with enhanced metadata
            from langchain.schema import Document
            enhanced_doc = Document(
                page_content=doc.page_content,
                metadata=enhanced_metadata
            )
            enhanced_documents.append(enhanced_doc)
        
        # Add documents using the standard add_documents method
        db.add_documents(enhanced_documents)
        logger.info(f"Added {len(documents)} documents to category: {category}")
        
    except Exception as e:
        logger.error(f"Failed to add documents: {str(e)}")
        raise