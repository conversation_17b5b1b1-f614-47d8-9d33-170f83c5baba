{% extends "admin_base.html" %}

{% block title %}View Form - {{ form.name }}{% endblock %}

{% block content %}
<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <!-- Header with form info and back button -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h6 class="mb-1">Form Preview</h6>
                        <p class="text-muted mb-0">This is how the form will appear to users</p>
                    </div>
                    <a href="{{ url_for('list_forms') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Forms
                    </a>
                </div>

                <!-- Form Information Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Form Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> {{ form.name }}</p>
                                <p><strong>Status:</strong> 
                                    {% if form.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Created:</strong> {{ form.created_at }}</p>
                                <p><strong>Last Updated:</strong> {{ form.updated_at }}</p>
                            </div>
                        </div>
                        {% if form.description %}
                        <div class="mt-3">
                            <p><strong>Description:</strong></p>
                            <p class="text-muted">{{ form.description }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Form Preview Card -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>Form Preview
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Preview Mode Indicator -->
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Preview Mode:</strong> This is a read-only preview of how the form will appear to users. 
                            Form submission is disabled in preview mode.
                        </div>

                        <!-- Form Fields -->
                        <form class="preview-form" novalidate>
                            {% for field in form.fields %}
                            <div class="mb-3">
                                <label for="{{ field.name }}" class="form-label">
                                    {{ field.label }}
                                    {% if field.required %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>
                                
                                {% if field.type == 'textarea' %}
                                    <textarea class="form-control" 
                                              id="{{ field.name }}" 
                                              name="{{ field.name }}" 
                                              rows="4"
                                              placeholder="{{ field.placeholder or '' }}"
                                              disabled></textarea>
                                {% elif field.type == 'email' %}
                                    <input type="email" 
                                           class="form-control" 
                                           id="{{ field.name }}" 
                                           name="{{ field.name }}" 
                                           placeholder="{{ field.placeholder or '' }}"
                                           disabled>
                                {% elif field.type == 'tel' %}
                                    <input type="tel" 
                                           class="form-control" 
                                           id="{{ field.name }}" 
                                           name="{{ field.name }}" 
                                           placeholder="{{ field.placeholder or '' }}"
                                           disabled>
                                {% elif field.type == 'number' %}
                                    <input type="number" 
                                           class="form-control" 
                                           id="{{ field.name }}" 
                                           name="{{ field.name }}" 
                                           placeholder="{{ field.placeholder or '' }}"
                                           disabled>
                                {% else %}
                                    <input type="{{ field.type or 'text' }}" 
                                           class="form-control" 
                                           id="{{ field.name }}" 
                                           name="{{ field.name }}" 
                                           placeholder="{{ field.placeholder or '' }}"
                                           disabled>
                                {% endif %}
                                
                                {% if field.required %}
                                    <div class="form-text text-muted">
                                        <i class="fas fa-asterisk text-danger me-1"></i>Required field
                                    </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                            
                            <!-- Submit Button (Disabled in Preview) -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary" disabled>
                                    <i class="fas fa-paper-plane me-2"></i>Submit Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Form Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Form Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ form.fields|length }}</h4>
                                    <p class="text-muted mb-0">Total Fields</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ form.fields|selectattr('required', 'equalto', true)|list|length }}</h4>
                                    <p class="text-muted mb-0">Required Fields</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ form.fields|selectattr('type', 'equalto', 'textarea')|list|length }}</h4>
                                    <p class="text-muted mb-0">Text Areas</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ form.fields|selectattr('type', 'in', ['email', 'tel', 'number'])|list|length }}</h4>
                                    <p class="text-muted mb-0">Special Inputs</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Usage Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ usage_stats.total_pdfs }}</h4>
                                    <p class="text-muted mb-0">PDFs Using This Form</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ usage_stats.total_submissions }}</h4>
                                    <p class="text-muted mb-0">Total Submissions</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ usage_stats.recent_submissions }}</h4>
                                    <p class="text-muted mb-0">Recent (30 days)</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ associated_pdfs|length }}</h4>
                                    <p class="text-muted mb-0">Active PDFs</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if usage_stats.top_pdfs %}
                        <div class="mt-4">
                            <h6>Most Popular PDFs:</h6>
                            <div class="list-group list-group-flush">
                                {% for pdf in usage_stats.top_pdfs %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>{{ pdf.filename }}</span>
                                    <span class="badge bg-primary rounded-pill">{{ pdf.submission_count }} submissions</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Associated PDFs -->
                {% if associated_pdfs %}
                <div class="card mt-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-pdf me-2"></i>Associated PDF Documents
                            </h5>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllPdfs">
                                    <i class="fas fa-check-square me-1"></i>Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="selectNonePdfs">
                                    <i class="fas fa-square me-1"></i>Select None
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" id="deleteSelectedPdfs" disabled>
                                    <i class="fas fa-trash me-1"></i>Delete Selected (<span id="selectedCount">0</span>)
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="pdfTable">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                                        </th>
                                        <th>Status</th>
                                        <th>Filename</th>
                                        <th>Title & Author</th>
                                        <th>Category</th>
                                        <th>Upload Date</th>
                                        <th>Size</th>
                                        <th>Pages</th>
                                        <th>Submissions</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for pdf in associated_pdfs %}
                                    <tr data-pdf-id="{{ pdf.id }}" data-pdf-filename="{{ pdf.original_filename }}">
                                        <td>
                                            <input type="checkbox" class="form-check-input pdf-checkbox" value="{{ pdf.id }}">
                                        </td>
                                        <td>
                                            <span class="file-status-indicator" data-pdf-id="{{ pdf.id }}" data-category="{{ pdf.category }}" data-filename="{{ pdf.filename }}">
                                                <i class="fas fa-spinner fa-spin text-muted" title="Checking file status..."></i>
                                            </span>
                                        </td>
                                        <td>
                                            <!-- Display original filename for user-friendliness, system filename for reference -->
                                            <strong>{{ pdf.original_filename }}</strong>
                                            <br><small class="text-muted">System filename: {{ pdf.filename }}</small>
                                        </td>
                                        <td>
                                            {% if pdf.pdf_title %}
                                                <div class="fw-semibold text-truncate" style="max-width: 250px;" title="{{ pdf.pdf_title }}">
                                                    {{ pdf.pdf_title }}
                                                </div>
                                            {% endif %}
                                            {% if pdf.pdf_author %}
                                                <div class="small text-muted text-truncate" style="max-width: 250px;" title="{{ pdf.pdf_author }}">
                                                    {{ pdf.pdf_author }}
                                                </div>
                                            {% endif %}
                                            {% if not pdf.pdf_title and not pdf.pdf_author %}
                                                <span class="text-muted small">No metadata available</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ pdf.category }}</span>
                                        </td>
                                        <td>{{ pdf.upload_date[:10] if pdf.upload_date else 'N/A' }}</td>
                                        <td>
                                            {% if pdf.file_size %}
                                                {% if pdf.file_size > 1048576 %}
                                                    {{ (pdf.file_size / 1024 / 1024) | round(1) }} MB
                                                {% else %}
                                                    {{ (pdf.file_size / 1024) | round(1) }} KB
                                                {% endif %}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                        <td>{{ pdf.page_count or 'N/A' }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ pdf.submission_count }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('view_pdf_details', pdf_id=pdf.id) }}"
                                                   class="btn btn-outline-info"
                                                   title="View PDF Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                                {# Always use the system filename for the download link #}
                                                <a href="{{ url_for('download_gated_pdf', filename=pdf.filename) }}"
                                                   class="btn btn-outline-primary"
                                                   title="View Download Page">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <a href="{{ url_for('list_submissions') }}?pdf_id={{ pdf.id }}"
                                                   class="btn btn-outline-secondary"
                                                   title="View Submissions">
                                                    <i class="fas fa-clipboard-list"></i>
                                                </a>
                                                <button type="button"
                                                        class="btn btn-outline-danger delete-pdf-btn"
                                                        data-pdf-id="{{ pdf.id }}"
                                                        data-pdf-filename="{{ pdf.original_filename }}"
                                                        data-form-id="{{ form.id }}"
                                                        title="Delete PDF Record">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-pdf me-2"></i>Associated PDF Documents
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No PDF documents are currently using this form.</p>
                            <p class="small">When you upload a PDF and select this form as the "Gated Download Form", it will appear here.</p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ url_for('list_forms') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Forms
                    </a>
                    <div>
                        <a href="{{ url_for('upload_file') }}?form_id={{ form.id }}" class="btn btn-success me-2">
                            <i class="fas fa-upload me-2"></i>Upload PDF with This Form
                        </a>
                        <a href="{{ url_for('edit_form', form_id=form.id) }}" class="btn btn-info me-2">
                            <i class="fas fa-edit me-2"></i>Edit Form
                        </a>
                        <a href="{{ url_for('list_submissions') }}" class="btn btn-warning">
                            <i class="fas fa-clipboard-list me-2"></i>View Submissions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-form .form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.8;
    cursor: not-allowed;
}

.preview-form .btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check file status for all PDFs
    checkAllFileStatus();

    // Batch selection functionality
    const selectAllBtn = document.getElementById('selectAllPdfs');
    const selectNoneBtn = document.getElementById('selectNonePdfs');
    const deleteSelectedBtn = document.getElementById('deleteSelectedPdfs');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const selectedCountSpan = document.getElementById('selectedCount');

    // Update selected count and button state
    function updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.pdf-checkbox:checked');
        const count = checkboxes.length;
        selectedCountSpan.textContent = count;
        deleteSelectedBtn.disabled = count === 0;

        // Update select all checkbox state
        const allCheckboxes = document.querySelectorAll('.pdf-checkbox');
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // Select all functionality
    selectAllBtn.addEventListener('click', function() {
        document.querySelectorAll('.pdf-checkbox').forEach(cb => cb.checked = true);
        updateSelectedCount();
    });

    // Select none functionality
    selectNoneBtn.addEventListener('click', function() {
        document.querySelectorAll('.pdf-checkbox').forEach(cb => cb.checked = false);
        updateSelectedCount();
    });

    // Select all checkbox functionality
    selectAllCheckbox.addEventListener('change', function() {
        const checked = this.checked;
        document.querySelectorAll('.pdf-checkbox').forEach(cb => cb.checked = checked);
        updateSelectedCount();
    });

    // Individual checkbox change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('pdf-checkbox')) {
            updateSelectedCount();
        }
    });

    // Individual delete functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-pdf-btn')) {
            const btn = e.target.closest('.delete-pdf-btn');
            const pdfId = btn.dataset.pdfId;
            const pdfFilename = btn.dataset.pdfFilename;
            const formId = btn.dataset.formId;

            deleteSinglePdf(pdfId, pdfFilename, formId);
        }
    });

    // Batch delete functionality
    deleteSelectedBtn.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.pdf-checkbox:checked');
        const pdfIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        const formId = {{ form.id }};

        deleteBatchPdfs(pdfIds, formId);
    });

    // File status checking
    function checkAllFileStatus() {
        document.querySelectorAll('.file-status-indicator').forEach(indicator => {
            checkFileStatus(indicator);
        });
    }

    function checkFileStatus(indicator) {
        const pdfId = indicator.dataset.pdfId;
        const category = indicator.dataset.category;
        const filename = indicator.dataset.filename;

        // For now, we'll use a simple approach - you could implement a dedicated endpoint
        // to check file existence, but for this implementation, we'll show a generic status
        setTimeout(() => {
            // Simulate file status check - in a real implementation, you'd make an AJAX call
            // to check if the file exists on the filesystem
            indicator.innerHTML = '<i class="fas fa-question-circle text-warning" title="File status unknown - click delete to see details"></i>';
        }, 1000);
    }

    // Single PDF deletion
    function deleteSinglePdf(pdfId, pdfFilename, formId) {
        if (!confirm(`Are you sure you want to delete the PDF "${pdfFilename}"?\n\nThis will:\n- Remove the database record\n- Delete the file from the filesystem (if it exists)\n- Remove vector embeddings\n\nThis action cannot be undone.`)) {
            return;
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch(`/admin/forms/${formId}/pdf/${pdfId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                // Remove the row from the table
                const row = document.querySelector(`tr[data-pdf-id="${pdfId}"]`);
                if (row) {
                    row.remove();
                }
                updateSelectedCount();

                // Show details if available
                if (data.details) {
                    console.log('Deletion details:', data.details);
                }
            } else {
                showToast('error', `Failed to delete PDF: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'An error occurred while deleting the PDF');
        });
    }

    // Batch PDF deletion
    function deleteBatchPdfs(pdfIds, formId) {
        const count = pdfIds.length;
        if (!confirm(`Are you sure you want to delete ${count} selected PDF(s)?\n\nThis will:\n- Remove the database records\n- Delete the files from the filesystem (if they exist)\n- Remove vector embeddings\n\nThis action cannot be undone.`)) {
            return;
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch(`/admin/forms/${formId}/pdfs/delete-batch`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ pdf_ids: pdfIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', `Successfully deleted ${data.deleted_count} PDF(s)`);

                // Remove the rows from the table
                pdfIds.forEach(pdfId => {
                    const row = document.querySelector(`tr[data-pdf-id="${pdfId}"]`);
                    if (row) {
                        row.remove();
                    }
                });

                updateSelectedCount();

                // Show details if available
                if (data.details && data.details.length > 0) {
                    console.log('Batch deletion details:', data.details);
                }

                if (data.errors && data.errors.length > 0) {
                    console.warn('Batch deletion errors:', data.errors);
                    showToast('warning', `${data.deleted_count} deleted, ${data.failed_count} failed. Check console for details.`);
                }
            } else {
                showToast('error', `Failed to delete PDFs: ${data.error}`);
                if (data.errors && data.errors.length > 0) {
                    console.error('Batch deletion errors:', data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'An error occurred while deleting the PDFs');
        });
    }

    // Toast notification function with error handling
    function showToast(type, message) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'warning'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }
});
</script>
{% endblock %}