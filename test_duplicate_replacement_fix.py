#!/usr/bin/env python3
"""
Test script to verify the duplicate replacement fix works correctly.
This test verifies that when uploading a duplicate PDF with "replace" action,
the original filename is preserved without adding timestamps.
"""

import os
import sys
import tempfile
import logging
from datetime import datetime
from unittest.mock import Mock, patch

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_mock_file(filename, content=b'%PDF-1.4\n%Test PDF content'):
    """Create a mock file object for testing"""
    mock_file = Mock()
    mock_file.filename = filename
    mock_file.read.return_value = content
    mock_file.seek = Mock()
    return mock_file

def test_upload_regular_pdf_with_original_filename():
    """Test that upload_regular_pdf_with_ocr_detection preserves original filename when provided"""
    logger.info("Testing upload_regular_pdf_with_ocr_detection with original_filename parameter...")
    
    try:
        # Import the function we're testing
        from app.__main__ import upload_regular_pdf_with_ocr_detection
        
        # Create a mock file
        test_filename = "test_document.pdf"
        original_filename = "existing_document.pdf"  # This should be preserved
        mock_file = create_mock_file(test_filename)
        
        # Mock the embed_file_db_first function to capture the parameters
        with patch('app.__main__.embed_file_db_first') as mock_embed:
            mock_embed.return_value = (True, "Success")
            
            # Call the function with original_filename parameter
            success, message = upload_regular_pdf_with_ocr_detection(
                file=mock_file,
                category="TEST",
                original_filename=original_filename
            )
            
            # Verify the function was called
            assert mock_embed.called, "embed_file_db_first should have been called"
            
            # Get the call arguments
            call_args = mock_embed.call_args
            args, kwargs = call_args
            
            # Verify that original_filename was passed correctly
            assert 'original_filename' in kwargs, "original_filename should be in kwargs"
            assert kwargs['original_filename'] == original_filename, f"Expected {original_filename}, got {kwargs['original_filename']}"
            
            logger.info("✓ Test passed: original_filename parameter is correctly passed to embed_file_db_first")
            return True
            
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_upload_regular_pdf_without_original_filename():
    """Test that upload_regular_pdf_with_ocr_detection works without original_filename (backward compatibility)"""
    logger.info("Testing upload_regular_pdf_with_ocr_detection without original_filename parameter...")
    
    try:
        # Import the function we're testing
        from app.__main__ import upload_regular_pdf_with_ocr_detection
        
        # Create a mock file
        test_filename = "test_document.pdf"
        mock_file = create_mock_file(test_filename)
        
        # Mock the embed_file_db_first function to capture the parameters
        with patch('app.__main__.embed_file_db_first') as mock_embed:
            mock_embed.return_value = (True, "Success")
            
            # Call the function without original_filename parameter
            success, message = upload_regular_pdf_with_ocr_detection(
                file=mock_file,
                category="TEST"
            )
            
            # Verify the function was called
            assert mock_embed.called, "embed_file_db_first should have been called"
            
            # Get the call arguments
            call_args = mock_embed.call_args
            args, kwargs = call_args
            
            # Verify that original_filename is None (default value)
            assert 'original_filename' in kwargs, "original_filename should be in kwargs"
            assert kwargs['original_filename'] is None, f"Expected None, got {kwargs['original_filename']}"
            
            logger.info("✓ Test passed: function works correctly without original_filename parameter")
            return True
            
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_embed_file_db_first_filename_logic():
    """Test the filename logic in embed_file_db_first function"""
    logger.info("Testing embed_file_db_first filename logic...")
    
    try:
        # Import required modules
        from app.utils.embedding_db import embed_file_db_first
        from werkzeug.utils import secure_filename
        
        # Create a mock file
        test_filename = "test_document.pdf"
        original_filename = "existing_document.pdf"
        mock_file = create_mock_file(test_filename)
        
        # Test 1: With original_filename provided (should preserve it)
        logger.info("Test 1: With original_filename provided...")
        
        # Mock all the dependencies
        with patch('app.utils.embedding_db.get_db_connection'), \
             patch('app.utils.embedding_db.create_pdf_directory_structure') as mock_create_dir, \
             patch('app.utils.embedding_db.process_pdf') as mock_process, \
             patch('app.utils.embedding_db.create_pdf_record') as mock_create_record:
            
            mock_create_dir.return_value = {
                'pdf_path': '/test/path/existing_document.pdf',
                'pdf_dir': '/test/path'
            }
            mock_process.return_value = {'text': 'test content', 'metadata': {}}
            mock_create_record.return_value = True
            
            # Mock file operations
            with patch('builtins.open'), \
                 patch('os.path.exists', return_value=False), \
                 patch('shutil.copy2'):
                
                success, message = embed_file_db_first(
                    file=mock_file,
                    category="TEST",
                    original_filename=original_filename
                )
                
                # Verify create_pdf_directory_structure was called with the original filename
                mock_create_dir.assert_called_with("TEST", original_filename)
                logger.info("✓ Test 1 passed: original_filename is used when provided")
        
        # Test 2: Without original_filename (should create timestamped filename)
        logger.info("Test 2: Without original_filename...")
        
        with patch('app.utils.embedding_db.get_db_connection'), \
             patch('app.utils.embedding_db.create_pdf_directory_structure') as mock_create_dir, \
             patch('app.utils.embedding_db.process_pdf') as mock_process, \
             patch('app.utils.embedding_db.create_pdf_record') as mock_create_record:
            
            mock_create_dir.return_value = {
                'pdf_path': '/test/path/timestamped_test_document.pdf',
                'pdf_dir': '/test/path'
            }
            mock_process.return_value = {'text': 'test content', 'metadata': {}}
            mock_create_record.return_value = True
            
            # Mock file operations
            with patch('builtins.open'), \
                 patch('os.path.exists', return_value=False), \
                 patch('shutil.copy2'):
                
                success, message = embed_file_db_first(
                    file=mock_file,
                    category="TEST",
                    original_filename=None
                )
                
                # Verify create_pdf_directory_structure was called with a timestamped filename
                call_args = mock_create_dir.call_args[0]
                filename_used = call_args[1]
                
                # Should be in format: YYYYMMDDHHMMSS_test_document.pdf
                assert filename_used != test_filename, "Filename should be timestamped"
                assert filename_used.endswith(f"_{test_filename}"), f"Filename should end with _{test_filename}"
                assert len(filename_used.split('_')[0]) == 14, "Timestamp should be 14 characters (YYYYMMDDHHMMSS)"
                
                logger.info("✓ Test 2 passed: timestamped filename is created when original_filename is None")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Run all tests"""
    logger.info("Starting duplicate replacement fix tests...")
    logger.info("=" * 60)
    
    tests = [
        test_upload_regular_pdf_with_original_filename,
        test_upload_regular_pdf_without_original_filename,
        test_embed_file_db_first_filename_logic
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        logger.info("")
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} crashed: {str(e)}")
            failed += 1
    
    logger.info("")
    logger.info("=" * 60)
    logger.info(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All tests passed! The duplicate replacement fix is working correctly.")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
