# ERDB Document Management System - Unified Database Configuration

# Unified Database Path
DB_PATH=./erdb_main.db
USER_DB_PATH=./erdb_main.db
CONTENT_DB_PATH=./erdb_main.db

# Vector Database Paths (Chroma - Separate for performance)
CHROMA_PATH=./data/chroma/chroma

# Application Settings
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# AI Model Settings
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
#TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest
TEXT_EMBEDDING_MODEL=nomic-embed-text:latest
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M

# Ollama Settings
OLLAMA_BASE_URL=http://localhost:11434

# Database Optimization
SQLITE_JOURNAL_MODE=WAL
SQLITE_CACHE_SIZE=10000
SQLITE_TEMP_STORE=MEMORY

# File Storage
TEMP_FOLDER=./data/temp
UPLOAD_FOLDER=./data/temp

# Logging
LOG_LEVEL=INFO
